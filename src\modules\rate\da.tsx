import {DataController} from '../../base/baseController';
import { StorageContanst } from '../../Config/Contanst';
import { getDataToAsyncStorage } from '../../utils/AsyncStorage';
  export const getLikesRatingCourse = async (id: string[]) => {
    const courseController = new DataController('Like_Rating');
    const courseResult = await courseController.group({
      searchRaw: `@RatingId:{${id.join(' | ')}}`,
      reducers: 'GROUPBY 1 @RatingId REDUCE COUNT 0 AS totalLikes',
    });
    if (courseResult.code === 200) {
      return courseResult?.data;
    }
    return [];
  };
  export const getIsLikeRating = async (id: string[]) => {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const customer = new DataController('Customer');
    const customerResult = await customer.getListSimple({
      query: `@RatingId:{${id.join(' | ')}} @CustomerId:{${cusId}}`,
    });
    if (customerResult?.data) {
      return customerResult.data;
    }
    return [];
  };
